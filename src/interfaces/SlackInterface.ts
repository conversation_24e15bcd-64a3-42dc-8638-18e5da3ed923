import { MessageInterface, ChatResponse, SessionCreateRequest } from '../models/types';
import { ChatService } from '../core/ChatService';
import { ForaChat } from '../operations';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { logger } from '../utils/Logger';

// Future Slack interface using Slack SDK
export class SlackInterface implements MessageInterface {
  private chatService: ChatService;
  private slackClient?: any; // Would be Slack WebClient
  private sessions: Map<string, any> = new Map(); // Cache sessions by user ID

  constructor(chatService: ChatService) {
    this.chatService = chatService;
    // this.slackClient = new WebClient(process.env.SLACK_BOT_TOKEN); // Future implementation
  }

  private async getOrCreateSession(userId: string, teamId?: string, channelId?: string): Promise<any> {
    const sessionKey = `${teamId}_${userId}`;
    
    // Check cache first
    if (this.sessions.has(sessionKey)) {
      const session = this.sessions.get(sessionKey);
      // Update session activity
      try {
        await ForaChat.updateSessionActivity(session.id);
        return session;
      } catch (error) {
        // Session might be expired, remove from cache
        this.sessions.delete(sessionKey);
      }
    }

    // Try to get existing session from database
    try {
      const userIdentifier = `slack_${teamId}_${userId}`;
      const handle = await DBOS.startWorkflow(ForaChat).getSessionByUserAndChannel(userIdentifier, 'slack');
      let session = await handle.getResult();

      if (!session) {
        // Create new session
        const sessionRequest: SessionCreateRequest = {
          userIdentifier,
          channel: 'slack',
          metadata: {
            userId,
            teamId,
            channelId,
            platform: 'slack'
          }
        };

        const createHandle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await createHandle.getResult();
        logger.info(`Created new Slack session for user ${userId} in team ${teamId}: ${session.id}`);
      } else {
        // Update existing session activity
        await ForaChat.updateSessionActivity(session.id);
        logger.info(`Restored Slack session for user ${userId} in team ${teamId}: ${session.id}`);
      }

      // Cache the session
      this.sessions.set(sessionKey, session);
      return session;
    } catch (error) {
      logger.error(`Error managing Slack session for user ${userId}`, error);
      throw error;
    }
  }

  async sendMessage(message: string): Promise<void> {
    // Future implementation with Slack SDK
    console.log(`Slack would send: ${message}`);
    
    /*
    await this.slackClient.chat.postMessage({
      channel: channelId,
      text: message,
      // Additional Slack-specific formatting
    });
    */
  }

  async receiveMessage(): Promise<string> {
    // This would be handled by Slack event subscriptions in a real implementation
    throw new Error('Slack receiveMessage should be handled by event subscriptions');
  }

  formatResponse(response: ChatResponse): string {
    // Format for Slack - use Slack's rich formatting
    let formatted = `*${response.theme}*\n\n`;
    
    response.reply.forEach((message, index) => {
      // Use Slack emoji and formatting
      const characterEmoji = this.getCharacterEmoji(message.character);
      formatted += `${characterEmoji} *${message.character}:* ${message.text}\n\n`;
    });
    
    if (response.skills && response.skills.length > 0) {
      formatted += `🎯 *Skills:* ${response.skills.join(', ')}`;
    }
    
    return formatted;
  }

  private getCharacterEmoji(character: string): string {
    const emojiMap: Record<string, string> = {
      'Fora': ':briefcase:',
      'Jan': ':woman-technologist:',
      'Lou': ':man-office-worker:',
      'user': ':speech_balloon:',
      'system': ':gear:'
    };
    return emojiMap[character] || ':speech_balloon:';
  }

  // Slack event handler for incoming messages
  async handleSlackEvent(event: any): Promise<void> {
    try {
      if (event.type !== 'message' || event.subtype || !event.text) {
        return; // Ignore non-message events, bot messages, etc.
      }

      const { user, team, channel, text } = event;

      // Get or create session for this user
      const session = await this.getOrCreateSession(user, team, channel);

      // Log user request
      logger.info(`[SLACK] User request: "${text}" | User: ${user} | Team: ${team} | Channel: ${channel} | Session: ${session.id} | Conversation: ${session.conversation_id || 'new'}`);

      let result;
      if (session.conversation_id) {
        // Continue existing conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text, session.conversation_id);
        result = await handle.getResult();
      } else {
        // Start new conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text);
        result = await handle.getResult();

        // Update session with new conversation ID
        if (result.conversationId) {
          await ForaChat.updateSessionConversation(session.id, result.conversationId);
          session.conversation_id = result.conversationId;
          this.sessions.set(`${team}_${user}`, session); // Update cache
        }
      }

      const formattedResponse = this.formatResponse(result);

      // Send response back to Slack
      /*
      await this.slackClient.chat.postMessage({
        channel: channel,
        text: formattedResponse,
        thread_ts: event.ts // Reply in thread if desired
      });
      */

    } catch (error) {
      logger.error(`Error processing Slack event`, error);

      // Send error message to user
      /*
      await this.slackClient.chat.postMessage({
        channel: event.channel,
        text: 'Sorry, I had trouble processing your message. Please try again.'
      });
      */
    }
  }

  // Method to get session info for a user (useful for debugging/admin)
  async getSessionInfo(userId: string, teamId: string): Promise<any> {
    try {
      const userIdentifier = `slack_${teamId}_${userId}`;
      const handle = await DBOS.startWorkflow(ForaChat).getSessionByUserAndChannel(userIdentifier, 'slack');
      return await handle.getResult();
    } catch (error) {
      logger.error(`Error getting Slack session info for user ${userId}`, error);
      return null;
    }
  }

  // Method to clear session cache (useful for testing or admin operations)
  clearSessionCache(userId?: string, teamId?: string): void {
    if (userId && teamId) {
      const sessionKey = `${teamId}_${userId}`;
      this.sessions.delete(sessionKey);
      logger.info(`Cleared Slack session cache for user ${userId} in team ${teamId}`);
    } else {
      this.sessions.clear();
      logger.info('Cleared all Slack session cache');
    }
  }

  // Slack slash command handler
  async handleSlashCommand(command: any): Promise<string> {
    try {
      const { user_id, team_id, channel_id, text } = command;

      if (!text || text.trim().length === 0) {
        return 'Please provide a question or topic you\'d like help with. Example: `/forachat How do I give better feedback?`';
      }

      // Get or create session for this user
      const session = await this.getOrCreateSession(user_id, team_id, channel_id);

      // Log user request
      logger.info(`[SLACK-SLASH] User request: "${text}" | User: ${user_id} | Team: ${team_id} | Channel: ${channel_id} | Session: ${session.id} | Conversation: ${session.conversation_id || 'new'}`);

      let result;
      if (session.conversation_id) {
        // Continue existing conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text, session.conversation_id);
        result = await handle.getResult();
      } else {
        // Start new conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text);
        result = await handle.getResult();

        // Update session with new conversation ID
        if (result.conversationId) {
          await ForaChat.updateSessionConversation(session.id, result.conversationId);
        }
      }

      return this.formatResponse(result);

    } catch (error) {
      logger.error(`Error processing Slack slash command`, error);
      return 'Sorry, I had trouble processing your command. Please try again.';
    }
  }
}
